# 邮件监控系统使用说明

## 功能特性

这个邮件监控系统提供了以下功能：

### 🔗 连接功能
- 自动连接到 IMAP 邮件服务器
- 支持 SSL 加密连接
- 自动登录验证

### 📧 邮件查看
- **查看邮件列表**: 显示最新的邮件列表，包括主题、发件人和时间
- **查看邮件详情**: 查看完整的邮件内容，包括主题、发件人、收件人、时间和正文
- **邮件解码**: 自动解码各种编码格式的邮件头部和内容

### 🔄 实时监控
- **实时监控新邮件**: 每10秒检查一次是否有新邮件
- **新邮件提醒**: 发现新邮件时会显示提醒
- **自动刷新**: 发现新邮件后自动显示最新邮件列表

### 📊 状态查看
- **邮箱状态**: 显示总邮件数、最近邮件数和未读邮件数
- **连接状态**: 显示服务器连接和登录状态

## 使用方法

### 启动程序
```bash
python main.py
```

### 菜单选项

程序启动后会显示以下菜单：

```
📧 邮件监控系统
========================================
1. 查看邮件列表
2. 查看邮件详情
3. 开始实时监控
4. 刷新邮件
5. 查看邮箱状态
6. 退出
========================================
```

#### 1. 查看邮件列表
- 显示最新的10封邮件
- 包含邮件编号、主题、发件人和时间
- 邮件按时间倒序排列（最新的在前）

#### 2. 查看邮件详情
- 需要先执行"查看邮件列表"
- 输入邮件编号查看详细内容
- 显示完整的邮件信息和正文内容

#### 3. 开始实时监控
- 每10秒检查一次新邮件
- 发现新邮件时会显示提醒并自动刷新列表
- 按 `Ctrl+C` 可以停止监控

#### 4. 刷新邮件
- 手动刷新邮件列表
- 更新邮件计数

#### 5. 查看邮箱状态
- 显示邮箱的统计信息
- 包括总邮件数、最近邮件数、未读邮件数

#### 6. 退出
- 安全关闭连接并退出程序

## 技术特性

### 兼容性
- 使用标准的 IMAP 协议
- 兼容各种邮件服务器
- 自动处理不同的编码格式

### 安全性
- 使用 SSL 加密连接
- 安全的登录验证
- 自动连接管理

### 性能优化
- 只获取邮件头部信息进行列表显示（提高速度）
- 按需获取完整邮件内容
- 智能的邮件数量检测

## 错误处理

程序包含完善的错误处理机制：

- **连接错误**: 自动检测并报告连接问题
- **认证错误**: 显示登录失败信息
- **邮件解析错误**: 跳过有问题的邮件并继续处理
- **网络中断**: 优雅地处理网络中断情况

## 注意事项

1. **网络连接**: 确保网络连接正常
2. **邮件服务器**: 确保邮件服务器支持 IMAP 协议
3. **账户权限**: 确保邮箱账户允许 IMAP 访问
4. **防火墙**: 确保防火墙允许 IMAP 端口（通常是993）

## 自定义配置

如需修改邮件服务器配置，请编辑 `main.py` 文件中的以下变量：

```python
HOST = 'imap.2925.com'      # IMAP 服务器地址
PORT = 993                  # IMAP 端口（SSL）
USERNAME = '<EMAIL>' # 邮箱用户名
PASSWORD = 'your_password'  # 邮箱密码
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证服务器地址和端口
   - 确认防火墙设置

2. **登录失败**
   - 检查用户名和密码
   - 确认邮箱支持 IMAP
   - 检查是否需要应用专用密码

3. **无法获取邮件**
   - 检查邮箱权限设置
   - 确认收件箱不为空
   - 重试连接

### 调试模式

如果遇到问题，可以查看程序输出的详细错误信息，这些信息会帮助诊断问题。
