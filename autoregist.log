2025-08-02 15:16:33,726 - INFO - ✅ 配置文件加载成功
2025-08-02 15:16:33,729 - INFO - 🚀 启动 Augment Code 自动注册脚本
2025-08-02 15:16:33,729 - INFO - ==================================================
2025-08-02 15:16:33,729 - INFO - 当前配置:
2025-08-02 15:16:33,729 - INFO -   - 无头模式: False
2025-08-02 15:16:33,729 - INFO -   - 邮箱服务: gomail
2025-08-02 15:16:33,730 - INFO -   - 起始页面: https://app.augmentcode.com/account/subscription
2025-08-02 15:16:33,730 - INFO -   - 自动上传: True
2025-08-02 15:16:40,715 - INFO - ✅ 浏览器初始化完成
2025-08-02 15:16:40,715 - INFO - ✅ 邮箱服务初始化完成: gomail
2025-08-02 15:16:44,711 - INFO - Console: Failed to load resource: net::ERR_CONNECTION_RESET
2025-08-02 15:16:52,354 - INFO - Console: An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.
2025-08-02 15:17:08,365 - INFO - Console: Failed to load resource: net::ERR_CONNECTION_TIMED_OUT
2025-08-02 15:17:10,739 - ERROR - 运行失败: Page.goto: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://app.augmentcode.com/account/subscription", waiting until "load"

2025-08-02 15:17:11,004 - INFO - ✅ 资源清理完成
2025-08-02 15:17:46,062 - INFO - ✅ 配置文件加载成功
2025-08-02 15:17:46,065 - INFO - 🚀 启动 Augment Code 自动注册脚本
2025-08-02 15:17:46,066 - INFO - ==================================================
2025-08-02 15:17:46,066 - INFO - 当前配置:
2025-08-02 15:17:46,066 - INFO -   - 无头模式: False
2025-08-02 15:17:46,066 - INFO -   - 邮箱服务: gomail
2025-08-02 15:17:46,066 - INFO -   - 起始页面: https://login.augmentcode.com
2025-08-02 15:17:46,066 - INFO -   - 自动上传: True
2025-08-02 15:17:52,211 - INFO - ✅ 浏览器初始化完成
2025-08-02 15:17:52,211 - INFO - ✅ 邮箱服务初始化完成: gomail
2025-08-02 15:17:54,460 - INFO - 访问起始页面: https://login.augmentcode.com
2025-08-02 15:17:54,460 - INFO - 当前页面: https://www.augmentcode.com/
2025-08-02 15:17:54,460 - INFO - 未识别的页面，等待用户操作: https://www.augmentcode.com/
2025-08-02 15:17:59,483 - INFO - 当前页面: https://www.augmentcode.com/
2025-08-02 15:17:59,483 - INFO - 未识别的页面，等待用户操作: https://www.augmentcode.com/
2025-08-02 15:18:00,435 - INFO - Console: [Analytics] Vector loaded
2025-08-02 15:18:00,450 - INFO - Console: [Analytics] Loaded analytics services with consent
2025-08-02 15:18:00,521 - INFO - Console: [Analytics] Vector page tracked: Augment Code - AI coding platform for real software. {name: Augment Code - AI coding platform for real software., path: /, url: https://www.augmentcode.com/, search: , title: Augment Code - AI coding platform for real software.}
2025-08-02 15:18:01,591 - INFO - Console: Vector load method already called.
2025-08-02 15:18:04,491 - INFO - 当前页面: https://www.augmentcode.com/
2025-08-02 15:18:04,491 - INFO - 未识别的页面，等待用户操作: https://www.augmentcode.com/
2025-08-02 15:18:07,320 - INFO - Console: Failed to load resource: net::ERR_CONNECTION_RESET
2025-08-02 15:18:09,508 - INFO - 当前页面: https://login.augmentcode.com/u/login/identifier?state=hKFo2SBvc1RxcGhkb09LX2EyRFptS2VGTmliYW80bFctX0ZKSqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEppc1RGMHpnOWE1RFczQU1CSm1JRHJFQlhaWXdweUZvo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE
2025-08-02 15:18:09,508 - INFO - ===== 开始自动注册流程 =====
2025-08-02 15:18:21,821 - INFO - Console: Failed to load resource: net::ERR_CONNECTION_TIMED_OUT
2025-08-02 15:18:21,883 - INFO - Console: Failed to load resource: net::ERR_CONNECTION_TIMED_OUT
2025-08-02 15:18:25,831 - INFO - Console: Failed to load resource: net::ERR_QUIC_PROTOCOL_ERROR.QUIC_NETWORK_IDLE_TIMEOUT
2025-08-02 15:18:39,526 - ERROR - 处理注册页面失败: Timeout 30000ms exceeded.
2025-08-02 15:18:39,526 - INFO - ✅ 自动化流程执行完成
2025-08-02 15:18:39,808 - INFO - ✅ 资源清理完成
