#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Code 自动注册脚本 - 可视化UI界面
基于 Streamlit 的用户友好界面
"""

import streamlit as st
import json
import asyncio
import threading
import time
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

# 配置页面
st.set_page_config(
    page_title="Augment Code 自动注册工具",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        text-align: center;
        padding: 1rem 0;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .status-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin: 0.5rem 0;
    }
    .status-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin: 0.5rem 0;
    }
    .status-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin: 0.5rem 0;
    }
    .config-section {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self.default_config = {
            "headless": False,
            "email_service": "tempmail",
            "gomail_token": "",
            "start_url": "https://login.augmentcode.com",
            "auto_upload_invoice": True,
            "max_wait_time": 60,
            "debug_mode": False,
            "retry_attempts": 3,
            "auto_save": True,
            "proxy": {
                "enabled": False,
                "server": "",
                "username": "",
                "password": ""
            },
            "browser_config": {
                "viewport_width": 1920,
                "viewport_height": 1080,
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }
        }
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    merged_config = self.default_config.copy()
                    merged_config.update(config)
                    return merged_config
            except Exception as e:
                st.error(f"配置文件加载失败: {e}")
                return self.default_config.copy()
        else:
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            st.error(f"配置保存失败: {e}")
            return False

class LogHandler(logging.Handler):
    """自定义日志处理器，用于在UI中显示日志"""
    
    def __init__(self):
        super().__init__()
        self.logs = []
        self.max_logs = 1000
    
    def emit(self, record):
        log_entry = {
            'time': datetime.fromtimestamp(record.created).strftime('%H:%M:%S'),
            'level': record.levelname,
            'message': record.getMessage()
        }
        self.logs.append(log_entry)
        
        # 保持日志数量在限制内
        if len(self.logs) > self.max_logs:
            self.logs = self.logs[-self.max_logs:]
    
    def get_logs(self):
        return self.logs.copy()
    
    def clear_logs(self):
        self.logs.clear()

# 全局变量
if 'config_manager' not in st.session_state:
    st.session_state.config_manager = ConfigManager()

if 'log_handler' not in st.session_state:
    st.session_state.log_handler = LogHandler()
    # 配置日志
    logger = logging.getLogger()
    logger.addHandler(st.session_state.log_handler)
    logger.setLevel(logging.INFO)

if 'running' not in st.session_state:
    st.session_state.running = False

if 'config' not in st.session_state:
    st.session_state.config = st.session_state.config_manager.load_config()

def main():
    """主界面"""
    
    # 标题
    st.markdown("""
    <div class="main-header">
        <h1>🚀 Augment Code 自动注册工具</h1>
        <p>Python + Playwright 自动化脚本 - 可视化界面</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.header("⚙️ 配置设置")
        
        # 基础配置
        st.subheader("基础设置")
        
        headless = st.checkbox(
            "无头模式运行",
            value=st.session_state.config.get('headless', False),
            help="启用后浏览器将在后台运行，不显示窗口"
        )
        
        email_service = st.selectbox(
            "邮箱服务",
            options=["tempmail", "gomail"],
            index=0 if st.session_state.config.get('email_service') == 'tempmail' else 1,
            help="选择用于接收验证码的邮箱服务"
        )
        
        gomail_token = st.text_input(
            "GoMail Token",
            value=st.session_state.config.get('gomail_token', ''),
            type="password",
            help="如果使用 GoMail 服务，请输入您的 API Token"
        )
        
        start_url = st.selectbox(
            "起始页面",
            options=[
                "https://login.augmentcode.com",
                "https://www.augmentcode.com/resources/cursor",
                "https://app.augmentcode.com/account/subscription",
                "https://www.augmentcode.com"
            ],
            index=0,
            help="选择脚本开始执行的页面"
        )
        
        # 高级配置
        st.subheader("高级设置")
        
        auto_upload = st.checkbox(
            "自动上传账单",
            value=st.session_state.config.get('auto_upload_invoice', True),
            help="自动生成并上传PDF账单文件"
        )
        
        debug_mode = st.checkbox(
            "调试模式",
            value=st.session_state.config.get('debug_mode', False),
            help="启用详细的调试日志输出"
        )
        
        max_wait_time = st.slider(
            "最大等待时间（秒）",
            min_value=30,
            max_value=300,
            value=st.session_state.config.get('max_wait_time', 60),
            help="页面加载和操作的最大等待时间"
        )
        
        retry_attempts = st.slider(
            "重试次数",
            min_value=1,
            max_value=10,
            value=st.session_state.config.get('retry_attempts', 3),
            help="操作失败时的重试次数"
        )
        
        # 代理设置
        st.subheader("🌐 代理设置")
        
        proxy_enabled = st.checkbox(
            "启用代理",
            value=st.session_state.config.get('proxy', {}).get('enabled', False),
            help="通过代理服务器访问网站"
        )
        
        proxy_server = st.text_input(
            "代理服务器",
            value=st.session_state.config.get('proxy', {}).get('server', ''),
            placeholder="http://proxy.example.com:8080",
            help="代理服务器地址，格式：http://host:port 或 socks5://host:port"
        )
        
        proxy_username = st.text_input(
            "代理用户名",
            value=st.session_state.config.get('proxy', {}).get('username', ''),
            help="代理服务器用户名（如果需要认证）"
        )
        
        proxy_password = st.text_input(
            "代理密码",
            value=st.session_state.config.get('proxy', {}).get('password', ''),
            type="password",
            help="代理服务器密码（如果需要认证）"
        )
        
        # 自动保存
        auto_save = st.checkbox(
            "自动保存配置",
            value=st.session_state.config.get('auto_save', True),
            help="配置更改时自动保存"
        )
        
        # 更新配置
        new_config = {
            'headless': headless,
            'email_service': email_service,
            'gomail_token': gomail_token,
            'start_url': start_url,
            'auto_upload_invoice': auto_upload,
            'debug_mode': debug_mode,
            'max_wait_time': max_wait_time,
            'retry_attempts': retry_attempts,
            'auto_save': auto_save,
            'proxy': {
                'enabled': proxy_enabled,
                'server': proxy_server,
                'username': proxy_username,
                'password': proxy_password
            }
        }
        
        # 合并浏览器配置
        new_config.update({
            'browser_config': st.session_state.config.get('browser_config', {})
        })
        
        # 自动保存或手动保存
        if auto_save and new_config != st.session_state.config:
            st.session_state.config = new_config
            if st.session_state.config_manager.save_config(new_config):
                st.success("✅ 配置已自动保存")
        elif not auto_save:
            if st.button("💾 保存配置", use_container_width=True):
                st.session_state.config = new_config
                if st.session_state.config_manager.save_config(new_config):
                    st.success("✅ 配置保存成功")
                else:
                    st.error("❌ 配置保存失败")
    
    # 主内容区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("🎮 控制面板")
        
        # 运行状态
        if st.session_state.running:
            st.markdown('<div class="status-warning">⏳ 脚本正在运行中...</div>', unsafe_allow_html=True)
            if st.button("🛑 停止脚本", type="secondary", use_container_width=True):
                st.session_state.running = False
                st.rerun()
        else:
            st.markdown('<div class="status-success">✅ 脚本已就绪</div>', unsafe_allow_html=True)
            if st.button("🚀 启动脚本", type="primary", use_container_width=True):
                start_automation()
        
        # 配置预览
        st.subheader("📋 当前配置")
        
        config_display = st.session_state.config.copy()
        # 隐藏敏感信息
        if config_display.get('gomail_token'):
            config_display['gomail_token'] = '*' * 8
        if config_display.get('proxy', {}).get('password'):
            config_display['proxy']['password'] = '*' * 8
        
        st.json(config_display)
    
    with col2:
        st.header("📊 运行日志")
        
        # 日志控制
        col_clear, col_refresh = st.columns(2)
        with col_clear:
            if st.button("🗑️ 清空日志", use_container_width=True):
                st.session_state.log_handler.clear_logs()
                st.rerun()
        
        with col_refresh:
            if st.button("🔄 刷新", use_container_width=True):
                st.rerun()
        
        # 显示日志
        logs = st.session_state.log_handler.get_logs()
        
        if logs:
            log_container = st.container()
            with log_container:
                # 显示最近的日志（倒序）
                for log in reversed(logs[-50:]):  # 只显示最近50条
                    level_color = {
                        'INFO': '🔵',
                        'WARNING': '🟡',
                        'ERROR': '🔴',
                        'DEBUG': '⚪'
                    }.get(log['level'], '⚪')
                    
                    st.text(f"{level_color} {log['time']} - {log['message']}")
        else:
            st.info("暂无日志信息")

def start_automation():
    """启动自动化脚本"""
    st.session_state.running = True
    
    # 在新线程中运行自动化脚本
    def run_automation():
        try:
            # 这里应该调用实际的自动化脚本
            # 由于 Streamlit 的限制，我们需要使用不同的方法
            st.session_state.log_handler.emit(
                logging.LogRecord(
                    name="automation",
                    level=logging.INFO,
                    pathname="",
                    lineno=0,
                    msg="🚀 自动化脚本启动中...",
                    args=(),
                    exc_info=None
                )
            )
            
            # 模拟运行过程
            time.sleep(2)
            
            st.session_state.log_handler.emit(
                logging.LogRecord(
                    name="automation",
                    level=logging.INFO,
                    pathname="",
                    lineno=0,
                    msg="✅ 浏览器初始化完成",
                    args=(),
                    exc_info=None
                )
            )
            
        except Exception as e:
            st.session_state.log_handler.emit(
                logging.LogRecord(
                    name="automation",
                    level=logging.ERROR,
                    pathname="",
                    lineno=0,
                    msg=f"❌ 自动化脚本执行失败: {e}",
                    args=(),
                    exc_info=None
                )
            )
        finally:
            st.session_state.running = False
    
    # 启动线程
    thread = threading.Thread(target=run_automation)
    thread.daemon = True
    thread.start()
    
    st.rerun()

if __name__ == "__main__":
    main()
