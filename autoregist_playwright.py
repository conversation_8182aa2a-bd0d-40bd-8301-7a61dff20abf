#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Code 自动注册脚本 - Playwright 版本
实现与油猴脚本一致的功能逻辑
"""

import asyncio
import json
import time
import random
import string
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

from playwright.async_api import async_playwright, Page, Browser, BrowserContext
import requests
import aiohttp

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('autoregist.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EmailService:
    """邮箱服务基类"""
    
    async def create_email(self) -> Optional[str]:
        """创建临时邮箱"""
        raise NotImplementedError
    
    async def get_verification_code(self, email: str) -> Optional[str]:
        """获取验证码"""
        raise NotImplementedError

class TempMailService(EmailService):
    """TempMail Plus 服务"""
    
    def __init__(self):
        self.base_url = "https://tempmail.plus/api"
        self.email = None
        self.epin = None
    
    async def create_email(self) -> Optional[str]:
        """创建临时邮箱"""
        try:
            async with aiohttp.ClientSession() as session:
                # 生成随机用户名
                username = self._generate_username()
                
                # 获取可用域名
                async with session.get(f"{self.base_url}/domains") as resp:
                    if resp.status == 200:
                        domains = await resp.json()
                        domain = domains[0] if domains else "@tempmail.plus"
                    else:
                        domain = "@tempmail.plus"
                
                self.email = f"{username}{domain}"
                
                # 创建邮箱
                async with session.post(f"{self.base_url}/mails", 
                                      json={"email": self.email}) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        self.epin = data.get('epin')
                        logger.info(f"✅ 创建临时邮箱成功: {self.email}")
                        return self.email
                    
        except Exception as e:
            logger.error(f"创建临时邮箱失败: {e}")
        return None
    
    def _generate_username(self) -> str:
        """生成随机用户名"""
        first_names = ["john", "jane", "mike", "sarah", "david", "lisa", "tom", "anna"]
        last_names = ["smith", "johnson", "brown", "davis", "wilson", "moore", "taylor"]
        
        first = random.choice(first_names)
        last = random.choice(last_names)
        timestamp = str(int(time.time()))[-6:]
        random_num = ''.join(random.choices(string.digits, k=4))
        
        return f"{first}{last}{timestamp}{random_num}"
    
    async def get_verification_code(self, email: str) -> Optional[str]:
        """获取验证码"""
        if not self.epin:
            return None
            
        try:
            async with aiohttp.ClientSession() as session:
                for attempt in range(30):  # 最多尝试30次，每次2秒
                    url = f"{self.base_url}/mails?email={email}&limit=20&epin={self.epin}"
                    async with session.get(url) as resp:
                        if resp.status == 200:
                            mails = await resp.json()
                            if mails:
                                # 查找最新的验证邮件
                                for mail in mails:
                                    if 'augment' in mail.get('from', '').lower():
                                        content = mail.get('text', '') + mail.get('html', '')
                                        # 提取验证码
                                        import re
                                        code_match = re.search(r'\b\d{6}\b', content)
                                        if code_match:
                                            code = code_match.group()
                                            logger.info(f"✅ 获取到验证码: {code}")
                                            return code
                    
                    logger.info(f"等待验证邮件... ({attempt + 1}/30)")
                    await asyncio.sleep(2)
                    
        except Exception as e:
            logger.error(f"获取验证码失败: {e}")
        return None

class GoMailService(EmailService):
    """GoMail 服务"""
    
    def __init__(self, token: str):
        self.token = token
        self.base_url = "https://gomail.run/api/external"
        self.email = None
    
    async def create_email(self) -> Optional[str]:
        """创建临时邮箱"""
        try:
            headers = {
                'Authorization': f'Bearer {self.token}',
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                # 创建邮箱
                async with session.post(f"{self.base_url}/inbox", 
                                      headers=headers,
                                      json={"domain": "184772.xyz"}) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        if data.get('success'):
                            self.email = data['data']['email']
                            logger.info(f"✅ 创建 GoMail 邮箱成功: {self.email}")
                            return self.email
                    
        except Exception as e:
            logger.error(f"创建 GoMail 邮箱失败: {e}")
        return None
    
    async def get_verification_code(self, email: str) -> Optional[str]:
        """获取验证码"""
        try:
            headers = {
                'Authorization': f'Bearer {self.token}',
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                for attempt in range(30):
                    # 获取邮件列表
                    async with session.get(f"{self.base_url}/emails/{email}", 
                                         headers=headers) as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            if data.get('success') and data.get('data'):
                                emails = data['data']
                                for email_data in emails:
                                    if 'augment' in email_data.get('fromAddress', '').lower():
                                        # 获取邮件详情
                                        email_id = email_data['id']
                                        async with session.get(f"{self.base_url}/email/{email_id}", 
                                                             headers=headers) as detail_resp:
                                            if detail_resp.status == 200:
                                                detail_data = await detail_resp.json()
                                                if detail_data.get('success'):
                                                    content = detail_data['data'].get('textContent', '') + \
                                                            detail_data['data'].get('htmlContent', '')
                                                    
                                                    # 提取验证码
                                                    import re
                                                    code_match = re.search(r'\b\d{6}\b', content)
                                                    if code_match:
                                                        code = code_match.group()
                                                        logger.info(f"✅ 获取到验证码: {code}")
                                                        return code
                    
                    logger.info(f"等待验证邮件... ({attempt + 1}/30)")
                    await asyncio.sleep(2)
                    
        except Exception as e:
            logger.error(f"获取验证码失败: {e}")
        return None

class AugmentAutoRegister:
    """Augment Code 自动注册主类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.email_service: Optional[EmailService] = None
        
    async def setup_browser(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()

        # 浏览器配置
        browser_config = {
            'headless': self.config.get('headless', False),
            'args': [
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-dev-shm-usage',
                '--no-first-run',
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        }

        # 添加代理配置
        proxy_config = self.config.get('proxy', {})
        if proxy_config.get('enabled') and proxy_config.get('server'):
            browser_config['proxy'] = {
                'server': proxy_config['server']
            }
            if proxy_config.get('username') and proxy_config.get('password'):
                browser_config['proxy']['username'] = proxy_config['username']
                browser_config['proxy']['password'] = proxy_config['password']

            logger.info(f"🌐 使用代理服务器: {proxy_config['server']}")

        self.browser = await playwright.chromium.launch(**browser_config)

        # 创建上下文配置
        browser_settings = self.config.get('browser_config', {})
        context_config = {
            'viewport': {
                'width': browser_settings.get('viewport_width', 1920),
                'height': browser_settings.get('viewport_height', 1080)
            },
            'user_agent': browser_settings.get('user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'),
            'ignore_https_errors': True,  # 忽略 HTTPS 错误
            'java_script_enabled': True,
            'accept_downloads': True
        }

        # 如果浏览器级别没有设置代理，在上下文级别设置
        if not browser_config.get('proxy') and proxy_config.get('enabled') and proxy_config.get('server'):
            context_config['proxy'] = {
                'server': proxy_config['server']
            }
            if proxy_config.get('username') and proxy_config.get('password'):
                context_config['proxy']['username'] = proxy_config['username']
                context_config['proxy']['password'] = proxy_config['password']

        self.context = await self.browser.new_context(**context_config)
        self.page = await self.context.new_page()

        # 设置页面超时
        timeout = self.config.get('max_wait_time', 60) * 1000
        self.page.set_default_timeout(timeout)
        self.page.set_default_navigation_timeout(timeout)

        # 设置页面事件监听
        if self.config.get('debug_mode', False):
            self.page.on('console', lambda msg: logger.debug(f"Console: {msg.text}"))
            self.page.on('pageerror', lambda error: logger.error(f"Page Error: {error}"))
            self.page.on('requestfailed', lambda request: logger.warning(f"Request Failed: {request.url} - {request.failure}"))
        else:
            # 非调试模式下只记录错误
            self.page.on('pageerror', lambda error: logger.error(f"Page Error: {error}"))

        logger.info("✅ 浏览器初始化完成")

        # 自动保存配置（如果启用）
        if self.config.get('auto_save', True):
            save_config(self.config)
    
    async def setup_email_service(self):
        """初始化邮箱服务"""
        email_type = self.config.get('email_service', 'tempmail')
        
        if email_type == 'gomail':
            token = self.config.get('gomail_token')
            if not token:
                raise ValueError("GoMail token 未配置")
            self.email_service = GoMailService(token)
        else:
            self.email_service = TempMailService()
        
        logger.info(f"✅ 邮箱服务初始化完成: {email_type}")
    
    async def handle_resources_cursor_page(self):
        """处理 resources/cursor 页面"""
        logger.info("检测到 resources/cursor 页面，查找 'Get your free month' 按钮...")
        
        try:
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            
            # 多种方式查找按钮
            selectors = [
                'a[href="https://app.augmentcode.com/promotions/cursor"]',
                'a:has-text("Get your free month")',
                'a:has-text("free month")'
            ]
            
            for selector in selectors:
                try:
                    button = await self.page.wait_for_selector(selector, timeout=5000)
                    if button:
                        logger.info("找到 'Get your free month' 按钮，自动点击...")
                        await button.click()
                        logger.info("✅ 已点击 'Get your free month' 按钮！")
                        return True
                except:
                    continue
            
            logger.warning("未找到 'Get your free month' 按钮")
            return False
            
        except Exception as e:
            logger.error(f"处理 resources/cursor 页面失败: {e}")
            return False

    async def handle_subscription_page(self):
        """处理 account/subscription 页面"""
        logger.info("检测到 account/subscription 页面，检查可用额度...")

        try:
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)

            # 多次尝试检测余额
            max_attempts = 6
            for attempt in range(max_attempts):
                logger.info(f"第 {attempt + 1}/{max_attempts} 次尝试检测余额...")

                # 查找显示可用额度的元素
                selectors = [
                    'span.rt-Text.rt-r-size-5.rt-r-weight-medium',
                    'span[class*="rt-Text"]',
                    '*:has-text("available")'
                ]

                for selector in selectors:
                    try:
                        elements = await self.page.query_selector_all(selector)
                        for element in elements:
                            text = await element.text_content()
                            if text and 'available' in text:
                                logger.info(f"检查元素文本: '{text.strip()}'")

                                if '125 available' in text:
                                    logger.info("✅ 检测到 125 available，跳转到 resources/cursor 页面...")
                                    await self.page.goto('https://www.augmentcode.com/resources/cursor')
                                    return True
                                elif '725 available' in text:
                                    logger.info("🎉 检测到 725 available，账单上传成功！等待10秒后自动退出登录...")

                                    # 倒计时
                                    for i in range(10, 0, -1):
                                        logger.info(f"⏰ 等待 {i} 秒后执行 Logout...")
                                        await asyncio.sleep(1)

                                    # 查找并点击 Logout 按钮
                                    logout_selectors = [
                                        'button[data-testid="logout-button"]',
                                        'button.base-header-logout-button',
                                        'button:has-text("Logout")',
                                        'button:has-text("logout")'
                                    ]

                                    for logout_selector in logout_selectors:
                                        try:
                                            logout_button = await self.page.wait_for_selector(logout_selector, timeout=2000)
                                            if logout_button:
                                                logger.info("找到 Logout 按钮，自动点击...")
                                                await logout_button.click()
                                                logger.info("✅ 已点击 Logout 按钮！账单上传流程完成！")
                                                return True
                                        except:
                                            continue

                                    logger.warning("❌ 未找到 Logout 按钮")
                                    return False
                                else:
                                    logger.info(f"检测到其他额度: '{text.strip()}'，继续等待更新...")
                    except:
                        continue

                if attempt < max_attempts - 1:
                    logger.info("未找到余额信息，3秒后重试...")
                    await asyncio.sleep(3)

            logger.warning("❌ 多次尝试后仍未找到可用额度信息")
            return False

        except Exception as e:
            logger.error(f"处理 subscription 页面失败: {e}")
            return False

    async def handle_promotions_cursor_page(self):
        """处理 promotions/cursor 页面（文件上传）"""
        logger.info("检测到 promotions/cursor 页面，开始自动上传流程...")

        try:
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(2)

            # 检查是否已经完成上传（有 Return to Home 按钮）
            if await self._check_upload_completed():
                logger.info("检测到可点击的 Return to Home 按钮，账单已上传完成")
                return await self._click_return_home()

            # 检查是否需要上传账单
            if not await self._check_need_upload():
                logger.info("当前页面不需要上传账单，跳过上传流程")
                return True

            # 等待上传区域加载
            await asyncio.sleep(2)

            # 再次检查是否完成
            if await self._check_upload_completed():
                logger.info("等待期间检测到 Return to Home 按钮，账单已上传完成")
                return await self._click_return_home()

            # 创建并上传PDF文件
            pdf_file = self._create_invoice_pdf()
            if await self._upload_file(pdf_file):
                logger.info("文件上传成功，等待 Upload Invoice 按钮...")

                # 等待并点击 Upload Invoice 按钮
                if await self._click_upload_invoice_button():
                    # 等待并点击 Return to Home 按钮
                    return await self._click_return_home()
                else:
                    logger.error("❌ 未找到 Upload Invoice 按钮，请手动点击")
                    return False
            else:
                logger.error("文件上传失败")
                return False

        except Exception as e:
            logger.error(f"处理 promotions/cursor 页面失败: {e}")
            return False

    async def _check_upload_completed(self) -> bool:
        """检查是否已经完成上传"""
        try:
            button = await self.page.query_selector('button:has-text("Return to Home")')
            if button:
                is_visible = await button.is_visible()
                is_enabled = await button.is_enabled()
                logger.info(f"找到 Return to Home 按钮，可见性: {is_visible}, 可点击: {is_enabled}")
                return is_visible and is_enabled
        except:
            pass
        return False

    async def _check_need_upload(self) -> bool:
        """检查是否需要上传账单"""
        selectors = [
            '.drag-drop-zone',
            '[data-testid="file-upload-zone"]',
            '.upload-area',
            'input[type="file"]',
            '*:has-text("Upload Invoice")',
            '*:has-text("Drag and drop")',
            '*:has-text("Choose file")'
        ]

        for selector in selectors:
            try:
                element = await self.page.query_selector(selector)
                if element:
                    return True
            except:
                continue
        return False

    def _create_invoice_pdf(self) -> str:
        """创建模拟PDF账单文件"""
        current_date = datetime.now().strftime('%Y-%m-%d')
        invoice_number = f'INV-{int(time.time())}'

        pdf_content = f"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 200
>>
stream
BT
/F1 12 Tf
50 750 Td
(INVOICE) Tj
0 -20 Td
(Invoice Number: {invoice_number}) Tj
0 -20 Td
(Date: {current_date}) Tj
0 -20 Td
(Amount: $99.99) Tj
0 -20 Td
(Description: Cursor Pro Subscription) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000206 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
456
%%EOF"""

        # 保存到临时文件
        filename = f'cursor_invoice_{current_date}.pdf'
        filepath = Path(filename)
        filepath.write_text(pdf_content, encoding='utf-8')

        logger.info(f"创建模拟PDF账单: {filename}")
        return str(filepath.absolute())

    async def _upload_file(self, file_path: str) -> bool:
        """上传文件"""
        try:
            # 查找文件输入元素
            file_input = await self.page.query_selector('input[type="file"]')
            if file_input:
                await file_input.set_input_files(file_path)
                logger.info(f"文件上传成功: {file_path}")
                await asyncio.sleep(2)  # 等待文件处理
                return True
            else:
                logger.error("未找到文件输入元素")
                return False

        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            return False

    async def _click_upload_invoice_button(self) -> bool:
        """点击 Upload Invoice 按钮"""
        try:
            selectors = [
                'button.rt-reset.rt-BaseButton.rt-r-size-2.rt-variant-solid.rt-Button',
                'button:has-text("Upload Invoice")',
                'button[class*="rt-Button"]'
            ]

            for selector in selectors:
                try:
                    button = await self.page.wait_for_selector(selector, timeout=30000)
                    if button:
                        button_text = await button.text_content()
                        if 'Upload' in button_text or 'upload' in button_text:
                            is_enabled = await button.is_enabled()
                            is_visible = await button.is_visible()

                            if is_enabled and is_visible:
                                logger.info(f"找到可点击的按钮: '{button_text.strip()}'")
                                await button.click()
                                logger.info("✅ 已点击 Upload Invoice 按钮")
                                return True
                except:
                    continue

            logger.warning("未找到可点击的 Upload Invoice 按钮")
            return False

        except Exception as e:
            logger.error(f"点击 Upload Invoice 按钮失败: {e}")
            return False

    async def _click_return_home(self) -> bool:
        """点击 Return to Home 按钮"""
        try:
            button = await self.page.wait_for_selector('button:has-text("Return to Home")', timeout=15000)
            if button:
                is_visible = await button.is_visible()
                is_enabled = await button.is_enabled()

                if is_visible and is_enabled:
                    logger.info("找到可见的 Return to Home 按钮，自动点击...")
                    await button.click()
                    logger.info("✅ 已点击 Return to Home 按钮！等待跳转到 subscription 页面...")

                    # 等待页面跳转
                    await asyncio.sleep(3)

                    # 检查是否跳转到了 subscription 页面
                    current_url = self.page.url
                    if 'account/subscription' in current_url:
                        logger.info("已跳转到 subscription 页面，刷新页面以获取最新余额...")
                        await asyncio.sleep(2)
                        await self.page.reload()

                    return True
                else:
                    logger.info("Return to Home 按钮存在但不可见，继续等待...")

            logger.warning("等待 Return to Home 按钮超时")
            return False

        except Exception as e:
            logger.error(f"点击 Return to Home 按钮失败: {e}")
            return False

    async def handle_registration_page(self):
        """处理注册页面"""
        logger.info("===== 开始自动注册流程 =====")

        try:
            # 等待页面加载，增加超时时间
            logger.info("等待页面加载...")
            await self.page.wait_for_load_state('networkidle', timeout=30000)
            await asyncio.sleep(2)

            # 打印当前页面信息用于调试
            current_url = self.page.url
            page_title = await self.page.title()
            logger.info(f"当前页面URL: {current_url}")
            logger.info(f"页面标题: {page_title}")

            # 检查当前页面状态 - 使用更宽泛的选择器
            logger.info("检查页面元素...")

            # 邮箱输入相关元素
            email_selectors = [
                'input[name="username"]',
                'input[type="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="Email"]',
                'input[id*="email"]',
                'input[name*="email"]'
            ]

            # 验证码输入相关元素
            code_selectors = [
                'input[name="code"]',
                'input[placeholder*="code"]',
                'input[placeholder*="Code"]',
                'input[id*="code"]',
                'input[name*="verification"]'
            ]

            # 条款同意相关元素
            terms_selectors = [
                '#terms-of-service-checkbox',
                'input[type="checkbox"]',
                '*:has-text("terms")',
                '*:has-text("Terms")',
                '*:has-text("agree")'
            ]

            # 检查邮箱输入
            email_input = None
            for selector in email_selectors:
                try:
                    email_input = await self.page.query_selector(selector)
                    if email_input:
                        logger.info(f"找到邮箱输入框: {selector}")
                        break
                except:
                    continue

            # 检查验证码输入
            code_input = None
            for selector in code_selectors:
                try:
                    code_input = await self.page.query_selector(selector)
                    if code_input:
                        logger.info(f"找到验证码输入框: {selector}")
                        break
                except:
                    continue

            # 检查条款复选框
            terms_checkbox = None
            for selector in terms_selectors:
                try:
                    terms_checkbox = await self.page.query_selector(selector)
                    if terms_checkbox:
                        logger.info(f"找到条款复选框: {selector}")
                        break
                except:
                    continue

            # 根据找到的元素决定处理方式
            if email_input:
                logger.info("🎯 检测到邮箱输入页面")
                return await self._handle_email_input_page()
            elif code_input:
                logger.info("🎯 检测到验证码输入页面")
                return await self._handle_verification_page()
            elif terms_checkbox:
                logger.info("🎯 检测到条款同意页面")
                return await self._handle_terms_page()
            else:
                logger.warning("❌ 未识别的注册页面状态")

                # 尝试查找所有输入框和按钮，用于调试
                all_inputs = await self.page.query_selector_all('input')
                all_buttons = await self.page.query_selector_all('button')

                logger.info(f"页面上共有 {len(all_inputs)} 个输入框，{len(all_buttons)} 个按钮")

                # 显示前几个输入框的信息
                for i, input_elem in enumerate(all_inputs[:5]):
                    try:
                        input_type = await input_elem.get_attribute('type')
                        input_name = await input_elem.get_attribute('name')
                        input_placeholder = await input_elem.get_attribute('placeholder')
                        logger.info(f"  输入框 {i+1}: type={input_type}, name={input_name}, placeholder={input_placeholder}")
                    except:
                        pass

                return False

        except Exception as e:
            logger.error(f"处理注册页面失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

    async def _handle_email_input_page(self):
        """处理邮箱输入页面"""
        try:
            # 创建临时邮箱
            email = await self.email_service.create_email()
            if not email:
                logger.error("创建临时邮箱失败")
                return False

            # 输入邮箱
            email_input = await self.page.query_selector('input[name="username"]')
            await email_input.fill(email)
            logger.info(f"已输入邮箱: {email}")

            # 点击继续按钮
            continue_button = await self.page.query_selector('button[type="submit"]')
            if continue_button:
                await continue_button.click()
                logger.info("已点击继续按钮")

                # 等待跳转到验证码页面
                await self.page.wait_for_selector('input[name="code"]', timeout=10000)
                logger.info("已跳转到验证码输入页面")
                return True
            else:
                logger.error("未找到继续按钮")
                return False

        except Exception as e:
            logger.error(f"处理邮箱输入页面失败: {e}")
            return False

    async def _handle_verification_page(self):
        """处理验证码输入页面"""
        try:
            # 获取验证码
            email = self.email_service.email
            if not email:
                logger.error("邮箱地址为空")
                return False

            logger.info("等待接收验证码...")
            verification_code = await self.email_service.get_verification_code(email)

            if not verification_code:
                logger.error("获取验证码失败")
                return False

            # 输入验证码
            code_input = await self.page.query_selector('input[name="code"]')
            await code_input.fill(verification_code)
            logger.info(f"已输入验证码: {verification_code}")

            # 点击验证按钮
            verify_button = await self.page.query_selector('button[type="submit"]')
            if verify_button:
                await verify_button.click()
                logger.info("已点击验证按钮")

                # 等待跳转
                await asyncio.sleep(3)
                return True
            else:
                logger.error("未找到验证按钮")
                return False

        except Exception as e:
            logger.error(f"处理验证码页面失败: {e}")
            return False

    async def _handle_terms_page(self):
        """处理条款同意页面"""
        try:
            # 勾选条款复选框
            terms_checkbox = await self.page.query_selector('#terms-of-service-checkbox')
            if terms_checkbox:
                await terms_checkbox.check()
                logger.info("已勾选服务条款")

            # 点击注册按钮
            signup_button = await self.page.query_selector('button[type="submit"]')
            if signup_button:
                await signup_button.click()
                logger.info("已点击注册按钮")

                # 等待注册完成
                await asyncio.sleep(5)
                logger.info("✅ 注册流程完成！")
                return True
            else:
                logger.error("未找到注册按钮")
                return False

        except Exception as e:
            logger.error(f"处理条款页面失败: {e}")
            return False

    async def handle_main_page(self):
        """处理主页面 (www.augmentcode.com)"""
        logger.info("检测到主页面，查找登录或注册入口...")

        try:
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')

            # 查找登录/注册相关按钮
            login_selectors = [
                'a[href*="login"]',
                'a[href*="auth"]',
                'a[href*="signup"]',
                'a:has-text("Login")',
                'a:has-text("Sign up")',
                'a:has-text("Get started")',
                'button:has-text("Login")',
                'button:has-text("Sign up")'
            ]

            for selector in login_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=3000)
                    if element:
                        element_text = await element.text_content()
                        href = await element.get_attribute('href')
                        logger.info(f"找到登录/注册按钮: '{element_text}' -> {href}")

                        await element.click()
                        logger.info("已点击登录/注册按钮")
                        await asyncio.sleep(3)
                        return True
                except:
                    continue

            # 如果没找到按钮，尝试直接导航到登录页面
            logger.info("未找到登录按钮，直接导航到登录页面...")
            await self.page.goto('https://login.augmentcode.com/u/login/identifier')
            await asyncio.sleep(3)
            return True

        except Exception as e:
            logger.error(f"处理主页面失败: {e}")
            return False

    async def run(self):
        """主运行函数"""
        try:
            await self.setup_browser()
            await self.setup_email_service()

            # 根据配置决定起始页面
            start_url = self.config.get('start_url', 'https://login.augmentcode.com')

            # 增加页面加载超时时间
            self.page.set_default_timeout(60000)  # 60秒超时

            logger.info(f"访问起始页面: {start_url}")
            await self.page.goto(start_url, wait_until='networkidle')

            # 主循环处理不同页面
            max_iterations = 15  # 增加最大迭代次数
            iteration = 0

            while iteration < max_iterations:
                iteration += 1
                current_url = self.page.url
                logger.info(f"第 {iteration} 次迭代，当前页面: {current_url}")

                # 检查页面类型并处理
                if 'www.augmentcode.com/resources/cursor' in current_url:
                    logger.info("🎯 处理 Resources/Cursor 页面")
                    if await self.handle_resources_cursor_page():
                        await asyncio.sleep(3)
                        continue
                    else:
                        break

                elif 'app.augmentcode.com/account/subscription' in current_url:
                    logger.info("🎯 处理 Account/Subscription 页面")
                    if await self.handle_subscription_page():
                        await asyncio.sleep(3)
                        continue
                    else:
                        break

                elif '/promotions/cursor' in current_url:
                    logger.info("🎯 处理 Promotions/Cursor 页面")
                    if await self.handle_promotions_cursor_page():
                        await asyncio.sleep(3)
                        continue
                    else:
                        break

                elif ('login.augmentcode.com' in current_url or
                      'auth.augmentcode.com' in current_url):
                    logger.info("🎯 处理注册/登录页面")
                    if await self.handle_registration_page():
                        await asyncio.sleep(3)
                        continue
                    else:
                        break

                elif 'www.augmentcode.com' in current_url and current_url.endswith('/'):
                    logger.info("🎯 处理主页面")
                    if await self.handle_main_page():
                        await asyncio.sleep(3)
                        continue
                    else:
                        break

                else:
                    logger.info(f"⏳ 未识别的页面，等待用户操作或页面变化: {current_url}")

                    # 检查是否有常见的导航元素
                    try:
                        # 查找可能的导航链接
                        nav_links = await self.page.query_selector_all('a[href*="login"], a[href*="signup"], a[href*="auth"]')
                        if nav_links:
                            logger.info(f"发现 {len(nav_links)} 个可能的导航链接")
                            for link in nav_links[:3]:  # 只尝试前3个
                                href = await link.get_attribute('href')
                                text = await link.text_content()
                                logger.info(f"  - {text}: {href}")
                    except:
                        pass

                    await asyncio.sleep(5)
                    continue

            if iteration >= max_iterations:
                logger.warning("达到最大迭代次数，停止执行")

            logger.info("✅ 自动化流程执行完成")

        except Exception as e:
            logger.error(f"运行失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
        finally:
            await self.cleanup()

    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            logger.info("✅ 资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")

def load_config() -> Dict[str, Any]:
    """加载配置"""
    config_file = Path('config.json')

    # 默认配置
    default_config = {
        'headless': False,
        'email_service': 'tempmail',  # 'tempmail' 或 'gomail'
        'gomail_token': '',
        'start_url': 'https://login.augmentcode.com',
        'auto_upload_invoice': True,
        'max_wait_time': 60,
        'debug_mode': False,
        'retry_attempts': 3,
        'auto_save': True,
        'proxy': {
            'enabled': False,
            'server': '',
            'username': '',
            'password': ''
        },
        'browser_config': {
            'viewport_width': 1920,
            'viewport_height': 1080,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
    }

    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                # 深度合并配置
                merged_config = default_config.copy()
                for key, value in user_config.items():
                    if isinstance(value, dict) and key in merged_config and isinstance(merged_config[key], dict):
                        merged_config[key].update(value)
                    else:
                        merged_config[key] = value
                logger.info("✅ 配置文件加载成功")
                return merged_config
        except Exception as e:
            logger.warning(f"配置文件加载失败，使用默认配置: {e}")
    else:
        # 创建默认配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        logger.info("✅ 创建默认配置文件")

    return default_config

def save_config(config: Dict[str, Any]) -> bool:
    """保存配置到文件"""
    config_file = Path('config.json')
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        logger.info("✅ 配置保存成功")
        return True
    except Exception as e:
        logger.error(f"配置保存失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 Augment Code 自动注册脚本 - Playwright 版本")
    print("=" * 50)

    # 加载配置
    config = load_config()

    # 显示配置信息
    logger.info("当前配置:")
    logger.info(f"  - 无头模式: {config['headless']}")
    logger.info(f"  - 邮箱服务: {config['email_service']}")
    logger.info(f"  - 起始页面: {config['start_url']}")
    logger.info(f"  - 自动上传: {config['auto_upload_invoice']}")

    # 创建并运行自动注册器
    auto_register = AugmentAutoRegister(config)
    await auto_register.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断执行")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
    finally:
        print("\n程序执行结束")
