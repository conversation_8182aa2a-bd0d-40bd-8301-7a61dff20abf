#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Code 自动注册脚本启动器
"""

import asyncio
import sys
import argparse
from pathlib import Path

# 添加当前目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

from autoregist_playwright import main, load_config, logger

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Augment Code 自动注册脚本')
    
    parser.add_argument('--headless', action='store_true', 
                       help='无头模式运行（不显示浏览器窗口）')
    
    parser.add_argument('--email-service', choices=['tempmail', 'gomail'], 
                       default='tempmail', help='选择邮箱服务')
    
    parser.add_argument('--gomail-token', type=str, 
                       help='GoMail API Token')
    
    parser.add_argument('--start-url', type=str, 
                       default='https://login.augmentcode.com',
                       help='起始页面URL')
    
    parser.add_argument('--config', type=str, default='config.json',
                       help='配置文件路径')
    
    return parser.parse_args()

def update_config_from_args(config, args):
    """根据命令行参数更新配置"""
    if args.headless:
        config['headless'] = True
    
    if args.email_service:
        config['email_service'] = args.email_service
    
    if args.gomail_token:
        config['gomail_token'] = args.gomail_token
    
    if args.start_url:
        config['start_url'] = args.start_url
    
    return config

async def run_with_config(config):
    """使用指定配置运行脚本"""
    from autoregist_playwright import AugmentAutoRegister
    
    logger.info("🚀 启动 Augment Code 自动注册脚本")
    logger.info("=" * 50)
    
    # 显示配置信息
    logger.info("当前配置:")
    logger.info(f"  - 无头模式: {config['headless']}")
    logger.info(f"  - 邮箱服务: {config['email_service']}")
    logger.info(f"  - 起始页面: {config['start_url']}")
    logger.info(f"  - 自动上传: {config['auto_upload_invoice']}")
    
    # 创建并运行自动注册器
    auto_register = AugmentAutoRegister(config)
    await auto_register.run()

def main_cli():
    """命令行主函数"""
    try:
        # 解析命令行参数
        args = parse_args()
        
        # 加载配置
        config = load_config()
        
        # 根据命令行参数更新配置
        config = update_config_from_args(config, args)
        
        # 运行脚本
        asyncio.run(run_with_config(config))
        
    except KeyboardInterrupt:
        logger.info("用户中断执行")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)
    finally:
        print("\n程序执行结束")

if __name__ == "__main__":
    main_cli()
