@echo off
chcp 65001 >nul
title Augment Code 自动注册脚本

echo ========================================
echo   Augment Code 自动注册脚本 - Playwright 版本
echo ========================================
echo.

:: 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到 Python，请先安装 Python 3.8+
    pause
    exit /b 1
)

:: 检查依赖是否安装
echo 📦 检查依赖...
pip show playwright >nul 2>&1
if errorlevel 1 (
    echo 📥 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    
    echo 🌐 安装浏览器...
    playwright install chromium
    if errorlevel 1 (
        echo ❌ 浏览器安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查完成
echo.

:: 显示菜单
:menu
echo 请选择运行模式：
echo 1. 正常模式（显示浏览器窗口）
echo 2. 无头模式（后台运行）
echo 3. 使用 GoMail 服务
echo 4. 自定义起始页面
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto normal
if "%choice%"=="2" goto headless
if "%choice%"=="3" goto gomail
if "%choice%"=="4" goto custom
if "%choice%"=="5" goto exit
echo 无效选择，请重新输入
goto menu

:normal
echo 🚀 启动正常模式...
python run.py
goto end

:headless
echo 🚀 启动无头模式...
python run.py --headless
goto end

:gomail
echo 请输入 GoMail Token:
set /p token=Token: 
if "%token%"=="" (
    echo ❌ Token 不能为空
    goto menu
)
echo 🚀 启动 GoMail 模式...
python run.py --email-service gomail --gomail-token %token%
goto end

:custom
echo 请输入起始页面URL:
echo 1. https://login.augmentcode.com (注册页面)
echo 2. https://www.augmentcode.com/resources/cursor (资源页面)
echo 3. https://app.augmentcode.com/account/subscription (订阅页面)
echo 4. 自定义URL
set /p url_choice=请选择 (1-4): 

if "%url_choice%"=="1" set start_url=https://login.augmentcode.com
if "%url_choice%"=="2" set start_url=https://www.augmentcode.com/resources/cursor
if "%url_choice%"=="3" set start_url=https://app.augmentcode.com/account/subscription
if "%url_choice%"=="4" (
    set /p start_url=请输入URL: 
    if "!start_url!"=="" (
        echo ❌ URL 不能为空
        goto menu
    )
)

echo 🚀 启动自定义模式...
python run.py --start-url "%start_url%"
goto end

:exit
echo 👋 再见！
exit /b 0

:end
echo.
echo 程序执行完成
pause

:error
echo ❌ 执行过程中出现错误
pause
exit /b 1
