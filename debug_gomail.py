#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GoMail API 调试脚本
用于测试 GoMail API 的邮箱创建功能
"""

import requests
import json
import time

# GoMail API 配置
API_BASE = 'https://184772.xyz'
API_TOKEN = 'gm_ysKvA5NUa1EGadX4cxaFW77eLS0QXzYH'

def test_token_validation():
    """测试 Token 是否有效"""
    print("=" * 50)
    print("1. 测试 Token 验证")
    print("=" * 50)
    
    url = f"{API_BASE}/api/external/mailbox"
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                token_info = data.get('data', {})
                print(f"✅ Token 有效!")
                print(f"   - Token 名称: {token_info.get('tokenName')}")
                print(f"   - 使用限制: {token_info.get('usageLimit')}")
                print(f"   - 已使用: {token_info.get('usageCount')}")
                print(f"   - 剩余次数: {token_info.get('remainingUsage')}")
                print(f"   - 是否激活: {token_info.get('isActive')}")
                print(f"   - 是否可用: {token_info.get('usable')}")
                return True
            else:
                print(f"❌ Token 验证失败: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON 解析失败: {e}")
        return False

def test_create_mailbox_with_domain():
    """测试创建邮箱（指定域名）"""
    print("\n" + "=" * 50)
    print("2. 测试创建邮箱（指定域名 184772.xyz）")
    print("=" * 50)
    
    url = f"{API_BASE}/api/external/mailbox"
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json'
    }
    
    # 测试数据：指定域名为 184772.xyz
    test_data = {
        "prefix": "jasonallenmdsomryy672",
        "domain": "184772.xyz"
    }
    
    print(f"请求 URL: {url}")
    print(f"请求头: {headers}")
    print(f"请求体: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, headers=headers, json=test_data, timeout=10)
        print(f"\n状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                mailbox_info = data.get('data', {})
                email = mailbox_info.get('email')
                print(f"✅ 邮箱创建成功!")
                print(f"   - 邮箱地址: {email}")
                print(f"   - 邮箱ID: {mailbox_info.get('id')}")
                print(f"   - 创建时间: {mailbox_info.get('createdAt')}")
                print(f"   - 过期时间: {mailbox_info.get('expiresAt')}")
                print(f"   - 是否激活: {mailbox_info.get('isActive')}")
                print(f"   - 剩余使用次数: {data.get('remainingUsage')}")
                
                # 检查域名是否正确
                if email and '@' in email:
                    domain = email.split('@')[1]
                    if domain == '184772.xyz':
                        print(f"✅ 域名正确: {domain}")
                    else:
                        print(f"❌ 域名不正确: 期望 184772.xyz，实际 {domain}")
                
                return email
            else:
                print(f"❌ 邮箱创建失败: {data.get('message')}")
                return None
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON 解析失败: {e}")
        return None

def test_create_mailbox_without_domain():
    """测试创建邮箱（不指定域名）"""
    print("\n" + "=" * 50)
    print("3. 测试创建邮箱（不指定域名）")
    print("=" * 50)
    
    url = f"{API_BASE}/api/external/mailbox"
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json'
    }
    
    # 测试数据：空对象
    test_data = {}
    
    print(f"请求 URL: {url}")
    print(f"请求头: {headers}")
    print(f"请求体: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, headers=headers, json=test_data, timeout=10)
        print(f"\n状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                mailbox_info = data.get('data', {})
                email = mailbox_info.get('email')
                print(f"✅ 邮箱创建成功!")
                print(f"   - 邮箱地址: {email}")
                print(f"   - 邮箱ID: {mailbox_info.get('id')}")
                print(f"   - 创建时间: {mailbox_info.get('createdAt')}")
                print(f"   - 过期时间: {mailbox_info.get('expiresAt')}")
                print(f"   - 是否激活: {mailbox_info.get('isActive')}")
                print(f"   - 剩余使用次数: {data.get('remainingUsage')}")
                
                # 显示默认域名
                if email and '@' in email:
                    domain = email.split('@')[1]
                    print(f"📧 默认域名: {domain}")
                
                return email
            else:
                print(f"❌ 邮箱创建失败: {data.get('message')}")
                return None
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON 解析失败: {e}")
        return None

def test_get_emails(email):
    """测试获取邮件列表"""
    if not email:
        print("\n❌ 跳过邮件测试：没有有效的邮箱地址")
        return []

    print("\n" + "=" * 50)
    print(f"4. 测试获取邮件列表: {email}")
    print("=" * 50)

    url = f"{API_BASE}/api/external/emails/{email}"
    headers = {
        'Authorization': f'Bearer {API_TOKEN}'
    }

    print(f"请求 URL: {url}")
    print(f"请求头: {headers}")

    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"\n状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                email_data = data.get('data', {})
                emails = email_data.get('emails', [])
                print(f"✅ 邮件列表获取成功!")
                print(f"   - 邮箱地址: {email_data.get('email')}")
                print(f"   - 邮件总数: {email_data.get('totalCount')}")
                print(f"   - 当前邮件数: {len(emails)}")
                print(f"   - 剩余使用次数: {data.get('remainingUsage')}")

                if emails:
                    print("   - 邮件列表:")
                    for i, mail in enumerate(emails):
                        print(f"     [{i+1}] ID: {mail.get('id')}")
                        print(f"         发件人: {mail.get('fromAddress')}")
                        print(f"         主题: {mail.get('subject')}")
                        print(f"         接收时间: {mail.get('receivedAt')}")
                    return emails
                else:
                    print("   - 暂无邮件")
                    return []

            else:
                print(f"❌ 邮件列表获取失败: {data.get('message')}")
                return []
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
            return []

    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return []
    except json.JSONDecodeError as e:
        print(f"❌ JSON 解析失败: {e}")
        return []

def test_get_email_content(email_id):
    """测试获取邮件详细内容"""
    print("\n" + "=" * 50)
    print(f"5. 测试获取邮件内容: {email_id}")
    print("=" * 50)

    url = f"{API_BASE}/api/external/email/{email_id}"
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    print(f"请求 URL: {url}")
    print(f"请求头: {headers}")

    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"\n状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        # 检查响应内容类型
        content_type = response.headers.get('Content-Type', '')
        print(f"响应内容类型: {content_type}")

        # 如果返回的是HTML，说明API端点可能不正确
        if 'text/html' in content_type:
            print("❌ API 返回了 HTML 页面而不是 JSON 数据")
            print("可能的原因:")
            print("1. API 端点不正确")
            print("2. 需要不同的认证方式")
            print("3. 邮件ID格式不正确")
            print(f"响应内容前500字符: {response.text[:500]}...")
            return None

        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    email_content = data.get('data', {})
                    print(f"✅ 邮件内容获取成功!")
                    print(f"   - 邮件ID: {email_content.get('id')}")
                    print(f"   - 发件人: {email_content.get('fromAddress')}")
                    print(f"   - 收件人: {email_content.get('toAddress')}")
                    print(f"   - 主题: {email_content.get('subject')}")
                    print(f"   - 接收时间: {email_content.get('receivedAt')}")
                    print(f"   - 剩余使用次数: {data.get('remainingUsage')}")

                    # 打印邮件内容
                    print("\n📧 邮件内容:")
                    print("-" * 50)

                    # 打印文本内容
                    text_content = email_content.get('textContent')
                    if text_content:
                        print("📝 文本内容:")
                        print(text_content)
                        print("-" * 30)

                    # 打印HTML内容
                    html_content = email_content.get('htmlContent')
                    if html_content:
                        print("🌐 HTML内容:")
                        print(html_content)
                        print("-" * 30)

                    # 如果都没有内容
                    if not text_content and not html_content:
                        print("❌ 邮件内容为空")

                    # 打印附件信息（如果有）
                    attachments = email_content.get('attachments', [])
                    if attachments:
                        print(f"📎 附件 ({len(attachments)} 个):")
                        for i, attachment in enumerate(attachments):
                            print(f"   [{i+1}] 文件名: {attachment.get('filename')}")
                            print(f"       大小: {attachment.get('size')} bytes")
                            print(f"       类型: {attachment.get('contentType')}")

                    print("-" * 50)
                    return email_content

                else:
                    print(f"❌ 邮件内容获取失败: {data.get('message')}")
                    return None
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
                print(f"响应不是有效的JSON格式")
                return None
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return None

def print_all_email_contents(emails):
    """打印所有邮件的详细内容"""
    if not emails:
        print("\n📭 没有邮件可以显示内容")
        return

    print("\n" + "=" * 50)
    print(f"📧 显示所有邮件内容 (共 {len(emails)} 封)")
    print("=" * 50)

    for i, mail in enumerate(emails):
        email_id = mail.get('id')
        if email_id:
            print(f"\n📨 第 {i+1} 封邮件:")
            print(f"   ID: {email_id}")
            print(f"   发件人: {mail.get('fromAddress')}")
            print(f"   主题: {mail.get('subject')}")
            print(f"   时间: {mail.get('receivedAt')}")

            # 获取并显示邮件内容
            content = test_get_email_content(email_id)

            # 添加分隔线
            if i < len(emails) - 1:
                print("\n" + "🔄" * 50)
        else:
            print(f"\n❌ 第 {i+1} 封邮件没有有效的ID")

def main():
    """主函数"""
    print("GoMail API 调试工具")
    print(f"API 服务器: {API_BASE}")
    print(f"Token: {API_TOKEN[:10]}..." if len(API_TOKEN) > 10 else f"Token: {API_TOKEN}")
    
    # 检查 Token 配置
    if API_TOKEN == 'gm_your_token_here':
        print("\n❌ 请先在脚本中设置正确的 API_TOKEN!")
        print("   修改第 12 行: API_TOKEN = '您的实际token'")
        return
    
    # 1. 验证 Token
    if not test_token_validation():
        print("\n❌ Token 验证失败，停止测试")
        return
    
    # 2. 测试创建邮箱（指定域名）
    email_with_domain = test_create_mailbox_with_domain()
    
    # 3. 测试创建邮箱（不指定域名）
    email_without_domain = test_create_mailbox_without_domain()
    
    # 4. 测试获取邮件（使用指定域名的邮箱）
    emails = test_get_emails(email_with_domain)

    # 5. 显示所有邮件的详细内容
    print_all_email_contents(emails)
    
    print("\n" + "=" * 50)
    print("调试完成!")
    print("=" * 50)
    
    # 总结
    print("\n📋 总结:")
    if email_with_domain:
        domain = email_with_domain.split('@')[1] if '@' in email_with_domain else 'unknown'
        print(f"   - 指定域名创建的邮箱: {email_with_domain}")
        print(f"   - 实际域名: {domain}")
        if domain == '184772.xyz':
            print("   ✅ 域名设置成功!")
        else:
            print("   ❌ 域名设置失败，可能的原因:")
            print("     1. API 不支持自定义域名")
            print("     2. Token 权限不足")
            print("     3. 184772.xyz 域名未配置")
    
    if email_without_domain:
        domain = email_without_domain.split('@')[1] if '@' in email_without_domain else 'unknown'
        print(f"   - 默认创建的邮箱: {email_without_domain}")
        print(f"   - 默认域名: {domain}")

if __name__ == "__main__":
    main()
