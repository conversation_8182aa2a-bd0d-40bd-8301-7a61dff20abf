#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器连接测试脚本
用于测试网络连接和页面访问
"""

import asyncio
import logging
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_browser_connection():
    """测试浏览器连接"""
    playwright = None
    browser = None
    
    try:
        logger.info("🚀 启动浏览器测试...")
        
        # 启动 Playwright
        playwright = await async_playwright().start()
        
        # 启动浏览器
        browser = await playwright.chromium.launch(
            headless=False,
            args=[
                '--no-sandbox',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        # 创建页面
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        
        page = await context.new_page()
        page.set_default_timeout(30000)
        
        # 测试页面列表
        test_urls = [
            'https://www.google.com',
            'https://www.augmentcode.com',
            'https://login.augmentcode.com',
            'https://app.augmentcode.com'
        ]
        
        for url in test_urls:
            try:
                logger.info(f"📍 测试访问: {url}")
                
                response = await page.goto(url, wait_until='networkidle', timeout=30000)
                
                if response:
                    status = response.status
                    logger.info(f"✅ {url} - 状态码: {status}")
                    
                    # 获取页面信息
                    title = await page.title()
                    current_url = page.url
                    
                    logger.info(f"   标题: {title}")
                    logger.info(f"   实际URL: {current_url}")
                    
                    # 检查是否有重定向
                    if current_url != url:
                        logger.info(f"   🔄 发生重定向: {url} -> {current_url}")
                    
                    # 等待一下再测试下一个
                    await asyncio.sleep(2)
                else:
                    logger.warning(f"⚠️ {url} - 无响应")
                    
            except Exception as e:
                logger.error(f"❌ {url} - 访问失败: {e}")
        
        logger.info("✅ 浏览器测试完成")
        
    except Exception as e:
        logger.error(f"浏览器测试失败: {e}")
        
    finally:
        # 清理资源
        if browser:
            await browser.close()
        if playwright:
            await playwright.stop()

async def test_augment_login_page():
    """专门测试 Augment 登录页面"""
    playwright = None
    browser = None
    
    try:
        logger.info("🎯 专门测试 Augment 登录页面...")
        
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        # 设置事件监听
        page.on('console', lambda msg: logger.info(f"Console: {msg.text}"))
        page.on('pageerror', lambda error: logger.error(f"Page Error: {error}"))
        page.on('requestfailed', lambda request: logger.warning(f"Request Failed: {request.url}"))
        
        # 访问登录页面
        logger.info("访问登录页面...")
        await page.goto('https://login.augmentcode.com', wait_until='networkidle')
        
        # 等待页面加载
        await asyncio.sleep(3)
        
        # 获取页面信息
        title = await page.title()
        url = page.url
        
        logger.info(f"页面标题: {title}")
        logger.info(f"当前URL: {url}")
        
        # 查找页面元素
        logger.info("查找页面元素...")
        
        # 查找所有输入框
        inputs = await page.query_selector_all('input')
        logger.info(f"找到 {len(inputs)} 个输入框:")
        
        for i, input_elem in enumerate(inputs):
            try:
                input_type = await input_elem.get_attribute('type')
                input_name = await input_elem.get_attribute('name')
                input_placeholder = await input_elem.get_attribute('placeholder')
                input_id = await input_elem.get_attribute('id')
                
                logger.info(f"  输入框 {i+1}: type={input_type}, name={input_name}, id={input_id}, placeholder={input_placeholder}")
            except:
                pass
        
        # 查找所有按钮
        buttons = await page.query_selector_all('button')
        logger.info(f"找到 {len(buttons)} 个按钮:")
        
        for i, button in enumerate(buttons):
            try:
                button_text = await button.text_content()
                button_type = await button.get_attribute('type')
                
                logger.info(f"  按钮 {i+1}: text='{button_text}', type={button_type}")
            except:
                pass
        
        # 等待用户观察
        logger.info("等待 10 秒供观察...")
        await asyncio.sleep(10)
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        
    finally:
        if browser:
            await browser.close()
        if playwright:
            await playwright.stop()

async def main():
    """主函数"""
    print("🔧 Augment Code 浏览器连接测试")
    print("=" * 50)
    
    # 基础连接测试
    await test_browser_connection()
    
    print("\n" + "=" * 50)
    
    # 专门测试登录页面
    await test_augment_login_page()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断测试")
    except Exception as e:
        logger.error(f"测试失败: {e}")
