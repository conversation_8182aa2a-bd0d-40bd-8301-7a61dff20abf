# Augment Code 自动注册脚本 - Playwright 版本

这是一个使用 Python + Playwright 实现的 Augment Code 自动注册脚本，功能与油猴脚本完全一致。

## 🚀 功能特性

### 📧 多邮箱服务支持
- **TempMail Plus**：免费临时邮箱服务
- **GoMail**：需要 Token 的高级邮箱服务

### 🔄 完整自动化流程
1. **Resources/Cursor 页面**：自动点击 "Get your free month" 按钮
2. **Account/Subscription 页面**：
   - 检测 125 available → 跳转到 resources/cursor 页面
   - 检测 725 available → 等待10秒后自动退出登录
3. **Promotions/Cursor 页面**：自动上传PDF账单文件
4. **注册页面**：完整的邮箱注册流程

### 🎯 智能特性
- **页面状态检测**：智能识别当前页面并执行相应操作
- **余额更新检测**：自动刷新页面获取最新余额状态
- **文件自动生成**：创建模拟PDF账单文件
- **错误重试机制**：多次尝试确保操作成功

## 📦 安装依赖

```bash
# 安装 Python 依赖
pip install -r requirements.txt

# 安装 Playwright 浏览器
playwright install chromium
```

## ⚙️ 配置说明

编辑 `config.json` 文件：

```json
{
  "headless": false,              // 是否无头模式运行
  "email_service": "tempmail",    // 邮箱服务: "tempmail" 或 "gomail"
  "gomail_token": "gm_xxx",       // GoMail Token（如果使用 GoMail）
  "start_url": "https://login.augmentcode.com",  // 起始页面
  "auto_upload_invoice": true,    // 是否自动上传账单
  "max_wait_time": 60            // 最大等待时间（秒）
}
```

### 邮箱服务配置

#### TempMail Plus（推荐）
```json
{
  "email_service": "tempmail"
}
```

#### GoMail
```json
{
  "email_service": "gomail",
  "gomail_token": "gm_your_actual_token_here"
}
```

## 🎮 使用方法

### 基本运行
```bash
python autoregist_playwright.py
```

### 不同起始页面
```bash
# 从注册页面开始
python autoregist_playwright.py

# 修改 config.json 中的 start_url 可以从不同页面开始：
# - https://login.augmentcode.com (注册页面)
# - https://www.augmentcode.com/resources/cursor (资源页面)
# - https://app.augmentcode.com/account/subscription (订阅页面)
```

## 📋 执行流程

### 1. 完整注册流程
```
启动脚本 → 访问注册页面 → 创建临时邮箱 → 输入邮箱 → 获取验证码 
→ 输入验证码 → 同意条款 → 注册完成 → 跳转到其他页面继续处理
```

### 2. 账单上传流程
```
检测上传页面 → 创建PDF账单 → 自动上传文件 → 点击Upload按钮 
→ 点击Return to Home → 跳转到subscription页面 → 检测余额 → 自动退出
```

### 3. 页面跳转流程
```
Resources页面 → 点击"Get your free month" → Promotions页面 → 上传账单 
→ Subscription页面 → 检测余额 → 根据余额执行相应操作
```

## 🔧 高级功能

### 自定义PDF账单
脚本会自动生成包含以下信息的PDF账单：
- 发票号码：INV-{时间戳}
- 日期：当前日期
- 金额：$99.99
- 描述：Cursor Pro Subscription

### 智能页面检测
- 自动识别当前页面类型
- 根据页面状态执行相应操作
- 支持页面跳转和状态变化

### 错误处理
- 网络超时重试
- 元素查找失败重试
- 页面加载异常处理

## 📊 日志输出

脚本会输出详细的执行日志：
- 控制台实时输出
- 保存到 `autoregist.log` 文件
- 包含时间戳和操作详情

## 🛠️ 故障排除

### 常见问题

1. **浏览器启动失败**
   ```bash
   playwright install chromium
   ```

2. **邮箱服务连接失败**
   - 检查网络连接
   - 验证 GoMail Token（如果使用）

3. **页面元素找不到**
   - 页面可能已更新，需要调整选择器
   - 增加等待时间

4. **验证码获取失败**
   - 检查邮箱服务状态
   - 增加重试次数

### 调试模式
设置 `headless: false` 可以看到浏览器操作过程，便于调试。

## 🔒 安全说明

- 脚本仅用于自动化测试和学习目的
- 请遵守相关网站的使用条款
- 不要用于恶意注册或滥用服务

## 📝 更新日志

- v1.0.0：初始版本，实现完整功能
- 支持多邮箱服务
- 智能页面处理
- 自动文件上传
- 余额检测和自动退出

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！
