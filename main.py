import imaplib
import email
import time
import datetime
from email.header import decode_header
import re


class EmailMonitor:
    def __init__(self, host, port, username, password):
        """初始化邮件监控器"""
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.conn = None
        self.last_email_count = 0

    def connect(self):
        """连接到邮件服务器"""
        try:
            self.conn = imaplib.IMAP4_SSL(host=self.host, port=self.port)
            print(f'✓ 已连接到服务器 {self.host}:{self.port}')

            self.conn.login(self.username, self.password)
            print(f'✓ 已登录用户 {self.username}')

            # 选择收件箱
            self.conn.select('INBOX')
            print('✓ 已选择收件箱')
            return True

        except Exception as e:
            print(f'✗ 连接失败: {e}')
            return False

    def reconnect(self):
        """重新连接"""
        print('🔄 尝试重新连接...')
        self.close()
        return self.connect()

    def ensure_connection(self):
        """确保连接有效"""
        try:
            # 尝试执行一个简单的命令来测试连接
            self.conn.noop()
            return True
        except:
            # 连接失效，尝试重连
            return self.reconnect()

    def decode_mime_words(self, s):
        """解码邮件头部信息"""
        if s is None:
            return ""

        decoded_parts = decode_header(s)
        decoded_string = ""

        for part, encoding in decoded_parts:
            if isinstance(part, bytes):
                if encoding:
                    try:
                        decoded_string += part.decode(encoding)
                    except:
                        decoded_string += part.decode('utf-8', errors='ignore')
                else:
                    decoded_string += part.decode('utf-8', errors='ignore')
            else:
                decoded_string += str(part)

        return decoded_string

    def get_email_list(self, count=10):
        """获取最新的邮件列表"""
        try:
            # 确保连接有效
            if not self.ensure_connection():
                print('✗ 无法建立连接')
                return [], 0

            # 获取邮件数量信息
            status, data = self.conn.status('INBOX', '(MESSAGES)')
            if status != 'OK':
                print('✗ 获取邮件状态失败')
                return [], 0

            # 解析邮件总数
            import re
            match = re.search(r'MESSAGES (\d+)', data[0].decode())
            if not match:
                print('✗ 无法解析邮件数量')
                return [], 0

            total_emails = int(match.group(1))
            if total_emails == 0:
                print('📭 收件箱为空')
                return [], 0

            # 计算要获取的邮件范围
            start_num = max(1, total_emails - count + 1)
            end_num = total_emails

            # 获取邮件ID列表
            email_ids = list(range(start_num, end_num + 1))
            email_ids.reverse()  # 最新的在前面

            emails = []
            for i, email_id in enumerate(email_ids):
                try:
                    # 获取邮件头部信息（更快）
                    status, msg_data = self.conn.fetch(str(email_id), '(BODY.PEEK[HEADER])')
                    if status != 'OK':
                        continue

                    # 解析邮件头部
                    msg = email.message_from_bytes(msg_data[0][1])

                    # 提取邮件信息
                    subject = self.decode_mime_words(msg['Subject']) or '(无主题)'
                    sender = self.decode_mime_words(msg['From']) or '(未知发件人)'
                    date = msg['Date']

                    # 解析日期
                    try:
                        parsed_date = email.utils.parsedate_to_datetime(date)
                        formatted_date = parsed_date.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        formatted_date = date or '(未知时间)'

                    emails.append({
                        'id': str(email_id),
                        'subject': subject,
                        'sender': sender,
                        'date': formatted_date,
                        'index': i + 1
                    })

                except Exception as e:
                    print(f'✗ 处理邮件 {email_id} 时出错: {e}')
                    continue

            return emails, total_emails

        except Exception as e:
            print(f'✗ 获取邮件列表失败: {e}')
            return [], 0

    def get_email_content(self, email_id):
        """获取指定邮件的详细内容"""
        try:
            # 确保连接有效
            if not self.ensure_connection():
                print('✗ 无法建立连接')
                return None

            # 获取邮件
            status, msg_data = self.conn.fetch(email_id, '(RFC822)')
            if status != 'OK':
                print('✗ 获取邮件失败')
                return None

            # 解析邮件
            msg = email.message_from_bytes(msg_data[0][1])

            # 提取邮件信息
            subject = self.decode_mime_words(msg['Subject'])
            sender = self.decode_mime_words(msg['From'])
            to = self.decode_mime_words(msg['To'])
            date = msg['Date']

            # 解析日期
            try:
                parsed_date = email.utils.parsedate_to_datetime(date)
                formatted_date = parsed_date.strftime('%Y-%m-%d %H:%M:%S')
            except:
                formatted_date = date

            # 提取邮件正文
            body = ""
            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))

                    if content_type == "text/plain" and "attachment" not in content_disposition:
                        try:
                            body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                            break
                        except:
                            continue
                    elif content_type == "text/html" and "attachment" not in content_disposition and not body:
                        try:
                            html_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                            # 简单的HTML标签清理
                            body = re.sub('<[^<]+?>', '', html_body)
                            break
                        except:
                            continue
            else:
                try:
                    body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
                except:
                    body = "无法解码邮件内容"

            return {
                'subject': subject,
                'sender': sender,
                'to': to,
                'date': formatted_date,
                'body': body
            }

        except Exception as e:
            print(f'✗ 获取邮件内容失败: {e}')
            return None

    def monitor_new_emails(self):
        """监控新邮件"""
        try:
            # 确保连接有效
            if not self.ensure_connection():
                return False

            # 获取当前邮件数量
            status, data = self.conn.status('INBOX', '(MESSAGES)')
            if status != 'OK':
                return False

            # 解析邮件总数
            match = re.search(r'MESSAGES (\d+)', data[0].decode())
            if not match:
                return False

            current_count = int(match.group(1))

            if current_count > self.last_email_count:
                new_emails = current_count - self.last_email_count
                print(f'\n🔔 发现 {new_emails} 封新邮件!')
                self.last_email_count = current_count
                return True

            return False

        except Exception as e:
            print(f'✗ 监控邮件失败: {e}')
            return False

    def display_email_list(self, emails, total_count):
        """显示邮件列表"""
        print(f'\n📧 邮件列表 (显示最新 {len(emails)} 封，共 {total_count} 封)')
        print('=' * 80)

        for email_info in emails:
            print(f"[{email_info['index']}] {email_info['subject'][:50]}")
            print(f"    发件人: {email_info['sender']}")
            print(f"    时间: {email_info['date']}")
            print('-' * 80)

    def display_email_content(self, email_content):
        """显示邮件详细内容"""
        if not email_content:
            print('✗ 无法显示邮件内容')
            return

        print('\n📧 邮件详情')
        print('=' * 80)
        print(f"主题: {email_content['subject']}")
        print(f"发件人: {email_content['sender']}")
        print(f"收件人: {email_content['to']}")
        print(f"时间: {email_content['date']}")
        print('-' * 80)
        print("内容:")
        print(email_content['body'][:1000])  # 限制显示长度
        if len(email_content['body']) > 1000:
            print('\n... (内容过长，已截断)')
        print('=' * 80)

    def get_mailbox_status(self):
        """获取邮箱状态信息"""
        try:
            # 确保连接有效
            if not self.ensure_connection():
                print('✗ 无法建立连接')
                return None

            # 获取邮箱状态
            status, data = self.conn.status('INBOX', '(MESSAGES RECENT UNSEEN)')
            if status != 'OK':
                print('✗ 获取邮箱状态失败')
                return None

            # 解析状态信息
            status_str = data[0].decode()
            messages_match = re.search(r'MESSAGES (\d+)', status_str)
            recent_match = re.search(r'RECENT (\d+)', status_str)
            unseen_match = re.search(r'UNSEEN (\d+)', status_str)

            return {
                'total': int(messages_match.group(1)) if messages_match else 0,
                'recent': int(recent_match.group(1)) if recent_match else 0,
                'unseen': int(unseen_match.group(1)) if unseen_match else 0
            }

        except Exception as e:
            print(f'✗ 获取邮箱状态失败: {e}')
            return None

    def display_mailbox_status(self):
        """显示邮箱状态"""
        status = self.get_mailbox_status()
        if status:
            print('\n📊 邮箱状态')
            print('=' * 40)
            print(f"📧 总邮件数: {status['total']}")
            print(f"🆕 最近邮件: {status['recent']}")
            print(f"👁️  未读邮件: {status['unseen']}")
            print('=' * 40)
        else:
            print('✗ 无法获取邮箱状态')

    def close(self):
        """关闭连接"""
        if self.conn:
            try:
                self.conn.close()
                self.conn.logout()
                print('✓ 已断开连接')
            except:
                pass


def show_menu():
    """显示菜单"""
    print('\n📧 邮件监控系统')
    print('=' * 40)
    print('1. 查看邮件列表')
    print('2. 查看邮件详情')
    print('3. 开始实时监控')
    print('4. 刷新邮件')
    print('5. 查看邮箱状态')
    print('6. 退出')
    print('=' * 40)


def main():
    """主函数"""
    # 邮件服务器配置
    HOST = 'imap.2925.com'
    PORT = 993
    USERNAME = '<EMAIL>'
    PASSWORD = 'E7D5KvJxtb'

    # 创建邮件监控器
    monitor = EmailMonitor(HOST, PORT, USERNAME, PASSWORD)

    # 连接到服务器
    if not monitor.connect():
        print('✗ 无法连接到邮件服务器，程序退出')
        return

    # 初始化邮件数量
    emails, total_count = monitor.get_email_list(1)
    monitor.last_email_count = total_count

    current_emails = []
    monitoring = False

    try:
        while True:
            if not monitoring:
                show_menu()
                choice = input('请选择操作 (1-6): ').strip()
            else:
                choice = '3'  # 继续监控

            if choice == '1':
                # 查看邮件列表
                print('\n正在获取邮件列表...')
                emails, total_count = monitor.get_email_list(10)
                current_emails = emails
                monitor.display_email_list(emails, total_count)

            elif choice == '2':
                # 查看邮件详情
                if not current_emails:
                    print('✗ 请先查看邮件列表')
                    continue

                try:
                    index = int(input('请输入要查看的邮件编号: ')) - 1
                    if 0 <= index < len(current_emails):
                        email_id = current_emails[index]['id']
                        print('\n正在获取邮件内容...')
                        content = monitor.get_email_content(email_id)
                        monitor.display_email_content(content)
                    else:
                        print('✗ 无效的邮件编号')
                except ValueError:
                    print('✗ 请输入有效的数字')

            elif choice == '3':
                # 开始/继续实时监控
                if not monitoring:
                    print('\n🔄 开始实时监控新邮件... (按 Ctrl+C 停止)')
                    monitoring = True

                try:
                    if monitor.monitor_new_emails():
                        # 有新邮件，显示最新邮件列表
                        emails, total_count = monitor.get_email_list(5)
                        current_emails = emails
                        monitor.display_email_list(emails, total_count)

                    time.sleep(10)  # 每10秒检查一次

                except KeyboardInterrupt:
                    print('\n⏹️  停止监控')
                    monitoring = False

            elif choice == '4':
                # 刷新邮件
                print('\n🔄 正在刷新邮件...')
                emails, total_count = monitor.get_email_list(10)
                current_emails = emails
                monitor.last_email_count = total_count
                monitor.display_email_list(emails, total_count)

            elif choice == '5':
                # 查看邮箱状态
                monitor.display_mailbox_status()

            elif choice == '6':
                # 退出
                print('\n👋 正在退出...')
                break

            else:
                if not monitoring:
                    print('✗ 无效的选择，请重新输入')

    except KeyboardInterrupt:
        print('\n\n👋 程序被用户中断')

    finally:
        monitor.close()


if __name__ == '__main__':
    main()